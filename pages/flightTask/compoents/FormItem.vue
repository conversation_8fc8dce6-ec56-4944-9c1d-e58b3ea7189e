<template>
  <view class="form-item"
        :class="required ? 'required' : '' +' '+ multiLine ? 'multi-line' : '' + ' ' + isChild ? 'is-child' : ''">
    <view class="label-box" :style="'width:'+labelWidth">
      {{ label }}
    </view>
    <view class="value-box" :style="!multiLine && 'width:calc(100% - '+labelWidth+' - 15px)'">
      <slot></slot>
    </view>
    <van-icon name="arrow-down" v-if="showIcon" class="right-icon"/>
  </view>
</template>
<script>
export default {
  name: "FormItem",
  props: {
    required: <PERSON><PERSON><PERSON>,
    label: String,
    showIcon: Boolean,
    labelWidth: {
      type: String,
      default: '100px'
    },
    multiLine: Boolean,//标题换行
    isChild: Boolean //是否是子组件
  }
}
</script>


<style scoped lang="scss">
.form-item {
  width: 100%;
  padding: 8px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  border-bottom: 1px solid #eee;
  flex-wrap: wrap;
  position: relative;
  box-sizing: border-box;

  .label-box {
    font-size: 14px;
    color: #323233;
  }
}

.multi-line {
  display: block;

  .label-box {
    width: 100%;
    text-align: left;
  }

  .value-box {
    width: 100%;
  }
}

.is-child {
  padding: 8px 16px;
  
}

.required {
  .label-box ::before {
    content: "*";
    position: absolute;
    left: -10px;
    top: 50%;
    transform: translateY(-50%);
    color: darkred;
    font-size: 14px;
  }
}

.right-icon {
  color: #999;
  font-size: 12px;
}
</style>

<template>
  <view class="add-fight-task">
    <!-- 自定义导航栏 -->
    <CustomerNav title="创建飞行任务书"/>

    <!-- 内容主体 -->
    <view class="content">
      <view class="form-box">
        <FormItem label="注册号" required show-icon>
          <input
              class="input-box"
              v-model="formData.registrationNumber"
              placeholder="请选择注册号"
              disabled
              @click="openPicker('注册号', registrationOptions, 'registrationNumber')"
          />
        </FormItem>
        <FormItem label="机型" required>
          <input
              class="input-box"
              v-model="formData.aircraftType"
              placeholder="请选择机型"
              disabled
          />
        </FormItem>
        <FormItem label="任务性质" required>
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in taskTypeList"
                  :class="formData.taskTypeValue === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onTaskTypeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>


            <view class="input-box-line" v-if="formData.taskTypeValue==='其他'">
              <input
                  v-model="formData.taskType"
                  placeholder="请选择"
                  disabled
                  @click="openPicker('任务性质', [], 'taskType')"
              />
              <van-icon name="arrow-down" class="right-icon"/>
            </view>
          </view>
        </FormItem>
        <FormItem label="计划时间" required>
          <view>
            <view class="btn-groups">
              <view
                  class="custom-tag"
                  v-for="item in planTimeList"
                  :class="formData.flightDateValue === item.value ? 'active' :''"
                  :key="item.value"
                  @click="onPlanTimeChange(item)"
              >
                {{ item.text }}
              </view>
            </view>

            <view class="input-box-line" v-if="formData.flightDateValue === '其他日期'">
              <input
                  v-model="formData.flightDate"
                  placeholder="请选择"
                  disabled
                  @click="datePickerShow = true"

              />
              <van-icon name="arrow-down" class="right-icon"/>
            </view>

          </view>
        </FormItem>
        <FormItem label="空游产品名称" required show-icon>
          <input
              class="input-box"
              v-model="formData.productName"
              placeholder="请选择空中游览产品名称"
              disabled
              @click="openPicker('空游产品名称', [], 'productName')"
          />
        </FormItem>
        <FormItem label="套餐名称" required show-icon>
          <input
              class="input-box"
              v-model="formData.packageName"
              placeholder="请选择套餐名称"
              disabled
              @click="openPicker('套餐名称', [], 'packageName')"
          />
        </FormItem>
        <FormItem label="预计架次" required>
          <input
              class="input-box"
              v-model="formData.flightFrequency"
              placeholder="请输入预计架次"
          />
        </FormItem>
        <FormItem label="计划起降时间" multi-line>
          <view>
            <FormItem label="第一架次" required is-child>
              <input
                  class="input-box"
                  v-model="formData.flightFrequency"
                  placeholder="请输入预计架次"
              />
            </FormItem>
          </view>
        </FormItem>
      </view>
    </view>

    <!-- 下拉选择-->
    <van-popup :show="pickerData.show" position="bottom">
      <van-picker
          :columns="pickerData.list"
          @confirm="onPickerConfirm"
          @cancel="closePicker"
          show-toolbar
          :title="pickerData.title"
      />
    </van-popup>
    <!-- 日期选择器 -->
    <van-calendar :show="datePickerShow"
                  @close="datePickerShow = false"
                  @confirm="onDateConfirm"
    />

  </view>
</template>

<script>
import CustomerNav from "../../components/CutomerNav/index.vue";
import FormItem from "./compoents/FormItem.vue";
import dayjs from "dayjs"
import {DATE_FORMAT} from "../../utils/constant";

export default {
  name: "createFlightTask",
  components: {FormItem, CustomerNav},
  data() {
    return {
      // 表单数据
      formData: {
        registrationNumber: '', // 注册号
        aircraftType: '', // 机型
        taskType: '', // 任务性质
        taskTypeValue: "",//任务性质选中按钮的值
        flightDate: "",//计划时间
        flightDateValue: "",//计划时间选中按钮的值
        productName: "",//产品名称
        packageName: "",//套餐名称
        flightFrequency: "",//架次
        takeOffAndLanding: []
      },
      //机型
      registrationOptions: [
        {text: 'B-7613', value: 'B-7613'},
        {text: 'B-7614', value: 'B-7614'},
        {text: 'B-7615', value: 'B-7615'}
      ],
      //任务类型
      taskTypeList: [
        {text: '空中游览', value: '空中游览'},
        {text: '空中交通', value: '空中交通'},
        {text: '其他', value: '其他'},
      ],
      //计划时间
      planTimeList: [
        {text: '今天', value: "今天"},
        {text: '明天', value: '明天'},
        {text: '其他日期', value: '其他日期'}
      ],
      //下拉选择数据
      pickerData: {
        show: false,
        title: "",
        list: [],
        formKey: "",
      },
      datePickerShow: false,
    }
  },

  methods: {
    openPicker(title, list, formKey) {
      this.pickerData = {
        show: true,
        title: title,
        list: list,
        formKey: formKey,
      }
    },
    closePicker() {
      this.pickerData = {
        show: false,
        title: "",
        list: [],
        formKey: "",
      }
    },
    onPickerConfirm(ev) {
      console.log(ev.detail)
      this.formData[this.pickerData.formKey] = ev.detail.value.value;
      if (this.pickerData.formKey === "registrationNumber") {
        this.formData.aircraftType = ev.detail.value.value;
      }
      this.closePicker();
    },
    onDateConfirm(ev) {
      this.formData.flightDate = dayjs(ev.detail).format(DATE_FORMAT);
      this.datePickerShow = false;
    },
    //任务性质按钮
    onTaskTypeChange(item) {
      this.formData.taskTypeValue = item.value;
    },
    //计划时间按钮
    onPlanTimeChange(item) {
      this.formData.flightDateValue = item.value;
    },

    // 提交表单
    submitForm() {
      // 表单验证
      if (!this.formData.registrationNumber) {
        uni.showToast({
          title: '请选择注册号',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.airspaceProductName) {
        uni.showToast({
          title: '请选择空域产品',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.packageName) {
        uni.showToast({
          title: '请选择套餐',
          icon: 'none'
        });
        return;
      }

      if (!this.formData.expectedFlights) {
        uni.showToast({
          title: '请输入预计航次',
          icon: 'none'
        });
        return;
      }

      // 这里可以调用API提交数据
      console.log('提交的表单数据:', this.formData);

      uni.showToast({
        title: '创建成功',
        icon: 'success'
      });

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack();
      }, 1500);
    }
  }
}
</script>

<style lang="scss" scoped>
.add-fight-task {
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
  padding: 16px;
}


// 内容区域
.content {
  padding: 20px 15px;
  margin-top: 88px;

  .btn-groups {
    width: 100%;
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
  }

  .custom-tag {
    padding: 0 4px;
    height: 24px;
    line-height: 24px;
    border: 1px solid #1989fa;
    color: #1989fa;
    background: transparent;
    font-size: 12px;

    &.active {
      background: #1989fa;
      color: #fff;
    }
  }

  input {
    font-size: 14px;
    font-weight: normal;

    &::placeholder {
      font-weight: normal;
      color: #999;
    }
  }

  .input-box-line {
    padding: 4px;
    //border: 1px solid #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 8px;
  }

  .mt-8 {
    margin-top: 8px;
  }

  .right-icon {
    color: #999;
    font-size: 12px;
  }

}
</style>
